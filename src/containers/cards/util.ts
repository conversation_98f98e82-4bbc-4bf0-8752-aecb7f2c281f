/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable no-plusplus */
/* eslint-disable @typescript-eslint/naming-convention */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */

import { numbersUnit } from "@/utils/helpers";

import { dateConverter } from "@/utils/DateHelper";
import getLineChartOptions, { defaultColors, renderRecord } from "../charts/util";
import {
  FOCUS_SUB_TABS,
  FUND,
  FUND_SUB_TABS,
  FUND_TABS,
  TFundRiskRow,
  TFundRow,
  TSummaryMarketRiskOptions
} from "./type";

export const isNullOrEmpty = (v?: number | null) => v == null || v === undefined;

export const convertFundData = (flatData: TFundRiskRow[]) => {
  const data: TFundRow[] = [];

  flatData.forEach(row => {
    for (const key in FUND) {
      const { title, desc = "", enum1 } = FUND[key];
      if (enum1.includes(row.fundRiskIndex)) {
        const f: number = data.findIndex(c => c.id === key);
        if (f === -1) {
          data.push({ id: key, title, desc, past: row, future: null });
        } else {
          data[f].future = row;
        }
      }
    }
  });

  return data;
};

export const getSummaryMarketRisksOptions = (future: number, past: number, checked?: boolean) => {
  const list = [];

  if (future !== -1) {
    list.push({ color: "#26D5C0", value: future });
  }

  if (checked) {
    list.push({ color: "#C7DA41", value: past });
  }
  return list;
};

export const getSummaryMarketEfficiencyOptions = (params: TSummaryMarketRiskOptions) => {
  const { future, past, real, isShowRetrospect, isShowExpected, isShowReal } = params;

  const list = [];

  if (isShowExpected) {
    list.push({ color: "#26D5C0", value: future });

    if (isShowRetrospect) {
      list.push({ color: "#C7DA41", value: past });
    }
  }

  if (isShowReal) {
    list.push({ color: "#F4F4F4", value: real });
  }

  return list;
};

export const getSummaryInterestOptions = (value1: number, value2?: number, colors?: string[]) => {
  const list = [{ color: colors && colors[0] ? colors[0] : "#55C3FF", value: value1 }];
  if (value2) {
    list.push({ color: colors && colors[1] ? colors[1] : "#43E5A0", value: value2 });
  }
  return list;
};

export const getSummaryInterestOptions2 = (values: number[], colors: string[]) => {
  const list: any[] = [];

  values.forEach((value, index) => {
    list.push({ value, color: colors[index] });
  });

  return list;
};

export const getSeriesOfMarketRiskFormat = (props: {
  graphPoints: any[];
  checked: boolean;
  futureField: string;
  pastField: string;
  hasPercent?: boolean;
  colorsArg?: string[];
}) => {
  const { graphPoints, checked, futureField, pastField, hasPercent, colorsArg } = props;
  const count = graphPoints?.length || 0;
  let colors = colorsArg || defaultColors;

  if (futureField === "") {
    colors = colors.slice(1);
  }

  const futureData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[futureField], index, count, color: colors?.[0] || null })
  );

  const pastData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[pastField], index, count, color: colors?.[1] || null })
  );

  const series = [];

  if (futureField !== "") {
    series.push({ name: "آینده نگر", data: futureData || [], type: "area", fillColor: "transparent" });
  }

  series.push({ name: "گذشته نگر", data: pastData || [], visible: checked, type: "area", fillColor: "transparent" });

  return getLineChartOptions({ data: series, hasPercent, chartColors: colors });
};

export const getSeriesOfEfficiencyMarketRiskFormat = (props: {
  graphPoints: any[];
  isShowRetrospect: boolean;
  isShowExpected: boolean;
  isShowReal: boolean;
  futureField: string;
  pastField: string;
  realField?: string;
  hasPercent?: boolean;
  colorsArg?: string[];
}) => {
  const {
    graphPoints,
    isShowRetrospect,
    isShowExpected,
    isShowReal,
    futureField,
    pastField,
    realField,
    hasPercent,
    colorsArg
  } = props;

  const count = graphPoints?.length || 0;
  const colors = colorsArg || defaultColors;

  const futureData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[futureField], index, count, color: colors?.[0] || null })
  );

  const pastData = graphPoints?.map((i, index) =>
    renderRecord({ dateTime: i.checkpointDate, value: i[pastField], index, count, color: colors?.[1] || null })
  );

  const realData = realField
    ? graphPoints?.map((i, index) =>
        renderRecord({ dateTime: i.checkpointDate, value: i[realField], index, count, color: colors?.[2] || null })
      )
    : [];

  const series = [];

  if (futureField !== "") {
    series.push({
      name: "آینده نگر",
      data: futureData || [],
      type: "area",
      fillColor: "transparent",
      visible: isShowExpected
    });
  }

  series.push({
    name: "گذشته نگر",
    data: pastData || [],
    visible: isShowRetrospect && isShowExpected,
    type: "area",
    fillColor: "transparent"
  });

  if (realData) {
    series.push({ name: "واقعی", visible: isShowReal, data: realData || [], type: "area", fillColor: "transparent" });
  }

  return getLineChartOptions({ data: series, hasPercent, chartColors: colors });
};

export const getSeriesOfInterestRiskFormat = (props: {
  graphPoints: any[];
  field1: { id: string; title: string };
  field2?: { id: string; title: string };
  moreFields?: { id: string; title: string }[];
  colors?: string[];
  checked?: boolean;
  hasPercent?: boolean;
}) => {
  const { graphPoints, field1, field2, moreFields, colors, hasPercent, checked = true } = props;
  const count = graphPoints?.length || 0;
  const chartColors = colors || ["#55C3FF", "#43E5A0"];
  let colorIndex = 0;

  const data1 = graphPoints
    ?.filter(item => item?.[field1?.id])
    ?.map((i, index) =>
      renderRecord({
        dateTime: i.checkpointDate,
        value: i[field1.id],
        index,
        count,
        color: chartColors?.[colorIndex] || null
      })
    );

  const series = [{ name: field1.title, data: data1 || [], type: "area", visible: true, fillColor: "transparent" }];

  if (field2) {
    colorIndex++;

    const data2 = graphPoints
      ?.filter(item => item?.[field2?.id])
      ?.map((i, index) =>
        renderRecord({
          dateTime: i.checkpointDate,
          value: i[field2?.id],
          index,
          count,
          color: chartColors?.[colorIndex] || null
        })
      );

    series.push({
      name: field2.title,
      data: data2 || [],
      type: "area",
      visible: checked,

      fillColor: "transparent"
    });
  }

  if (moreFields) {
    moreFields?.forEach(f => {
      colorIndex++;

      const data = graphPoints
        ?.filter(item => item?.[f?.id])
        ?.map((i, index) =>
          renderRecord({
            dateTime: i.checkpointDate,
            value: i[f.id],
            index,
            count,
            color: chartColors?.[colorIndex] || null
          })
        );

      series.push({ name: f.title, data: data || [], type: "area", visible: true, fillColor: "transparent" });
    });
  }

  return getLineChartOptions({ data: series, chartColors, hasPercent });
};

const hexToRgba = (hex: string, alpha: number) => {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

export const getSeriesOfInfoCard = (props: { graphPoints: any[]; field1: { id: string; title: string } }) => {
  const { graphPoints, field1 } = props;
  const count = graphPoints?.length || 0;
  const chartColors = ["#2A9AB0"];

  // map start
  const data1 = graphPoints
    ?.filter(item => item?.[field1?.id])
    ?.map((i, index) => {
      const list = renderRecord({
        dateTime: i.checkpointDate,
        value: i[field1.id],
        index,
        count,
        color: chartColors?.[0] || null
      });

      if (index < count - 1) {
        return { ...list, marker: { enabled: false } };
      }

      return { ...list, marker: { enabled: true, radius: 3, states: { hover: { enabled: false } } } };
    });
  // map end

  const series = [{ name: field1.title, data: data1 || [], type: "area", visible: true, fillColor: "transparent" }];

  const options = getLineChartOptions({ data: series, chartColors, hasPercent: false });
  const values = data1.map(i => i.y) || [];
  const max = Math.max.apply(null, values);
  const tickInterval = Math.floor(max / 4);

  return {
    ...options,
    chart: { ...options.chart, marginTop: 8, marginBottom: 5, marginRight: 5, zooming: { enabled: false } },
    xAxis: { visible: false },
    yAxis: {
      ...options.yAxis,
      startOnTick: false,
      gridLineColor: "#545454",
      tickInterval,
      labels: {
        useHTML: true,
        /* @ts-ignore */
        // eslint-disable-next-line
        formatter: function () {
          /* @ts-ignore */
          return this.value ? `${Math.trunc(toBillion(this.value))}B` : "0";
        },
        style: { color: "#F4F4F4", fontFamily: "var(--font-yekan)", fontSize: "10px" }
      }
    },
    tooltip: { enabled: false },
    plotOptions: { ...options.plotOptions, series: { ...options.plotOptions?.series, enableMouseTracking: false } }
  };
};

export const getSeriesOfTransactionFormat = ({
  data
}: {
  data?: {
    checkpointDate: string;
    totalDebitAmount: number;
    totalDepositAmount: number;
    depositTransactions: {
      reasonCategory: number;
      symbol: string;
      reasonDescription: string;
      contractNumber: string | null;
      bondOriginType: number;
      amount: number;
    }[];
    withdrawalTransactions: {
      reasonCategory: number;
      symbol: string;
      reasonDescription: string;
      contractNumber: string | null;
      bondOriginType: number;
      amount: number;
    }[];
  }[];
}) => {
  // Constants for better control
  const MAX_VISIBLE_COLUMNS = 32;
  const COLUMN_WIDTH = 18;
  const COLUMN_SPACING = 0.2;
  const MIN_COLUMN_HEIGHT = 15;

 

  const createDepositSeries = () => {
    const maxDepositCount = data?.length
      ? Math.max(...data.map(item => item.depositTransactions.length || 0))
      : 0;
    const depositSeries = [];

    for (let i = 0; i < maxDepositCount; i++) {
      depositSeries.push({
        name: `ورودی ${i + 1}`,
        stack: "deposit",
        colorByPoint: true,
        pointWidth: COLUMN_WIDTH,
        data:
          data
            ?.filter(item => !!item?.depositTransactions?.length)
            ?.map((item, index) => {
              const tx = item.depositTransactions[i];
              const amount = tx?.amount ?? 0;
              const colorHex =
                BondOriginTypes?.[tx?.reasonCategory as keyof typeof BondOriginTypes]?.color || "#00AA63";
              const baseOpacity = 0.4 + ((i + 1) / maxDepositCount) * 0.6;

              return {
                x: index + -0.2,
                y: tx ? amount : 0,
                date: item.checkpointDate, // Store date for tooltip
                color: {
                  linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
                  stops: [
                    [0, hexToRgba(colorHex, baseOpacity)],
                    [1, hexToRgba(colorHex, baseOpacity * 0.8)]
                  ]
                },
                ...(tx ? {} : { y: null })
              };
            }) || []
      });
    }

    return depositSeries;
  };

  const createWithdrawalSeries = () => {
    const maxWithdrawalCount = data?.length
      ? Math.max(...data.map(item => item.withdrawalTransactions.length || 0))
      : 0;
    const withdrawalSeries = [];

    for (let i = 0; i < maxWithdrawalCount; i++) {
      withdrawalSeries.push({
        name: `خروج ${i + 1}`,
        stack: "withdrawal",
        colorByPoint: true,
        pointWidth: COLUMN_WIDTH,
        data:
          data
            ?.filter(item => !!item?.withdrawalTransactions?.length)
            ?.map((item, index) => {
              const tx = item.withdrawalTransactions[i];
              const amount = tx?.amount ?? 0;
              const colorHex = BondOriginTypes[tx?.reasonCategory as keyof typeof BondOriginTypes]?.color || "#E51A1A";
              const baseOpacity = 0.4 + ((i + 1) / maxWithdrawalCount) * 0.6;

              return {
                x: index + 0.5,
                y: tx ? -amount : 0,
                date: item.checkpointDate,
                color: {
                  linearGradient: { x1: 0, y1: 1, x2: 0, y2: 0 },
                  stops: [
                    [0, hexToRgba(colorHex, baseOpacity)],
                    [1, hexToRgba(colorHex, baseOpacity * 0.8)]
                  ]
                },
                ...(tx ? {} : { y: null })
              };
            }) || []
      });
    }

    return withdrawalSeries;
  };

  const allSeries = [...createDepositSeries(), ...createWithdrawalSeries()];

  console.log("allSeries", allSeries);
  console.log("dataaaa", data);

  const allYValues = allSeries.flatMap(series => (series?.data ?? []).map(point => point.y).filter(y => y !== null));
  const dataMax = Math.max(...allYValues, 0);
  const dataLength = data?.length || 0;

  const needsScrolling = dataLength > MAX_VISIBLE_COLUMNS;

  return {
    chart: {
      type: "column",
      animation: false,
      backgroundColor: "transparent",
      marginTop: 50,
      marginBottom: 80,
      marginLeft: 60,
      marginRight: 40,
      zooming: {
        type: needsScrolling ? "x" : undefined
      },
      reflow: true,
      spacing: [10, 10, 15, 10]
    },
    title: { text: "" },
    subtitle: { text: "" },

    xAxis: {
      type: "linear",
      min: 0,
      max: needsScrolling ? MAX_VISIBLE_COLUMNS - 1 : dataLength - 1,

      scrollbar: {
        enabled: needsScrolling,
        barBackgroundColor: "#3a3a3a",
        barBorderRadius: 3,
        barBorderWidth: 0,
        buttonBackgroundColor: "#545454",
        buttonBorderWidth: 0,
        buttonBorderRadius: 3,
        trackBackgroundColor: "#1a1a1a",
        trackBorderWidth: 1,
        trackBorderRadius: 3,
        trackBorderColor: "#3a3a3a"
      },

      tickPositioner() {
        const axis = this;
        const min = Math.floor(axis.min || 0);
        const max = Math.floor(axis.max || dataLength - 1);
        const positions = [];

        for (let i = min; i <= max; i++) {
          positions.push(i);
        }

        return positions;
      },

      minTickInterval: 1,
      tickInterval: 1,

      labels: {
        formatter() {
          const index = Math.floor(this.value);
          const item = data?.[index];
          if (item) {
            const d = new Date(item.checkpointDate);
            return dateConverter(d.toString()).locale("fa").format("YYYY/MM/DD");
          }
          return "";
        },
        y: 20,
        rotation: -45,
        style: {
          color: "#F4F4F4",
          fontFamily: "yekan-bakh",
          fontSize: "10px",
          fontWeight: "400"
        },

        gridLineColor: "#3a3a3a",
        gridLineWidth: 1,
        gridLineDashStyle: "dot",

        tickColor: "#545454",
        tickWidth: 1,
        tickLength: 6,

        lineColor: "#545454",
        lineWidth: 1,

        minTickInterval: 1
      }
    },

    yAxis: {
      title: false,
      gridLineColor: "#545454",
      gridLineWidth: 1,
      gridLineDashStyle: "dash",
      min: -dataMax * 1.1,
      max: dataMax * 1.1,

      tickPositioner() {
        const maxAbs = Math.max(Math.abs(this.dataMin || 0), Math.abs(this.dataMax || 0));
        const step = maxAbs / 4;
        const ticks = [];

        for (let i = -4; i <= 4; i++) {
          ticks.push(Number((i * step).toFixed(0)));
        }

        return ticks;
      },

      labels: {
        overflow: "justify",
        formatter(): string {
          return numbersUnit(this?.value);
        },
        style: {
          color: "#F4F4F4",
          fontFamily: "yekan-bakh",
          fontSize: "11px"
        },
        x: -5
      },

      plotLines: [
        {
          value: 0,
          color: "#545454",
          width: 2,
          zIndex: 5
        }
      ]
    },

    tooltip: {
      useHTML: true,
      shadow: false,
      shared: false, // Changed to shared to capture all points at same x position
      backgroundColor: "#28282C",
      borderColor: "#858585",
      borderRadius: 8,
      borderWidth: 1,
      fontFamily: "yekan-bakh",
      padding: 12,
      hideDelay: 0,
      animation: false,

      formatter() {
        // Get the x position and find the corresponding data
        const x = this.x;
        const index = Math.round(x);
        const pointData = data?.[index];

        if (!pointData) return false;

        const hasDeposits =
          (pointData?.depositTransactions?.length ?? 0) > 0 && pointData.depositTransactions.some(tx => tx.amount > 0);
        const hasWithdrawals =
          (pointData?.withdrawalTransactions?.length ?? 0) > 0 &&
          pointData.withdrawalTransactions.some(tx => tx.amount > 0);

        if (!hasDeposits && !hasWithdrawals) return false;

        let s = `<div class="font-[yekan-bakh]"><p class="!text-[#FFFFFF] text-sm text-center mb-4 ">${dateConverter(
          pointData?.checkpointDate ?? ""
        ).format("YYYY/MM/DD")}</p>`;

        if (hasDeposits) {
          s += `<div class="flex items-center justify-between mb-3 !rtl w-[323px]" >
          <span class="text-[#FFFFFF] text-sm">${numbersUnit(pointData?.totalDebitAmount ?? 0)} </span>
          <span class="text-[#FFFFFF] text-sm">ورودی</span>
          </div>`;

          pointData?.depositTransactions?.forEach(tx => {
            if (tx.amount > 0) {
              s += `
                <div class="flex items-center justify-between text-white gap-2 mb-3">
                  <b class="whitespace-nowrap">${numbersUnit(tx?.amount)}  </b>
                  <div class="flex items-center gap-1">
                    <div class="flex items-center text-xs text-[#BDBDBD] flex-row-reverse ">
                    <span class=""> ${
                      BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes]?.title ?? ""
                    }</span>
                    <span class="mr-0.5">${tx.symbol ? `${tx.symbol}` : ""}<span>
                    <span class="mr-0.5">${tx.contractNumber ? `قراردارد ${tx.contractNumber}` : ""}<span>
                    <span class="mr-0.5">${
                      (BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes] as any)?.suffix ?? ""
                    }<span>
                  </div>
                   <i style="background:${BondOriginTypes[tx.reasonCategory as keyof typeof BondOriginTypes]
                     ?.color}; width:12px; height:12px; display:inline-block; border-radius:2px;"></i>
                  </div>
                </div>
              `;
            }
          });
        }

        if (hasWithdrawals) {
          const extraClass = hasDeposits ? "border-t border-t-[#676767] pt-3 mt-4 " : "";

          s += `<div class="flex items-center justify-between mb-3 w-[323px] text-left ${extraClass}">
          <span class="text-[#FFFFFF] text-sm">${
            pointData?.totalDepositAmount ? numbersUnit(Math.abs(pointData?.totalDepositAmount)) : 0
          } </span>
          <span class="text-[#FFFFFF] text-sm">خروجی</span>
          </div>`;

          pointData?.withdrawalTransactions?.forEach(tx => {
            if (tx.amount > 0) {
              s += `
                <div class="flex items-center justify-between text-white gap-2 mb-3">
            <b class="whitespace-nowrap">${tx?.amount ? numbersUnit(Math.abs(tx?.amount)) : 0} </b>
                  <div class="flex items-center gap-1">
                    <div class="flex items-center text-xs text-[#BDBDBD] text-right flex-row-reverse">
                    <span class="mr-0.5"> ${BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes]
                      ?.title}</span>
                    <span class="mr-0.5">${tx.symbol ? `${tx.symbol}` : ""}<span>
                    <span class="mr-0.5">${tx.contractNumber ? `قراردارد ${tx.contractNumber}` : ""}<span>
                    <span class="mr-0.5">${
                      (BondOriginTypes?.[tx.reasonCategory as keyof typeof BondOriginTypes] as any)?.suffix ?? ""
                    }<span>
                  </div>
                   <i style="background:${BondOriginTypes[tx.reasonCategory as keyof typeof BondOriginTypes]
                     ?.color}; width:12px; height:12px; display:inline-block; border-radius:2px;"></i>
                  </div>
                </div>
              `;
            }
          });
        }

        s += "</div>";
        return s;
      }
    },

    series: allSeries,

    plotOptions: {
      series: {
        states: {
          inactive: { enabled: false }
        },
        animation: false,
        turboThreshold: 0
      },
      column: {
        stacking: "normal",
        pointWidth: COLUMN_WIDTH,
        pointPadding: COLUMN_SPACING,
        groupPadding: COLUMN_SPACING,
        borderWidth: 0,
        minPointLength: MIN_COLUMN_HEIGHT,
        pointPlacement: 0,
        crisp: true,
        connectNulls: false,
        boostThreshold: 100
      }
    },

    ...(needsScrolling && {
      navigator: {
        enabled: true,
        height: 40,
        margin: 10,
        series: {
          type: "areaspline",
          color: "#4572A7",
          fillOpacity: 0.2,
          lineWidth: 1,
          marker: {
            enabled: false
          }
        },
        xAxis: {
          labels: {
            enabled: false
          },
          gridLineWidth: 0
        },
        yAxis: {
          labels: {
            enabled: false
          },
          gridLineWidth: 0
        }
      }
    }),

    credits: { enabled: false },
    legend: { enabled: false },

    responsive: {
      rules: [
        {
          condition: {
            maxWidth: 768
          },
          chartOptions: {
            plotOptions: {
              column: {
                pointWidth: 12,
                pointPadding: 0.1,
                groupPadding: 0.1
              }
            }
          }
        }
      ]
    }
  };
};

export const BondOriginTypes = {
  0: { name: "None", title: "نامشخص", color: "" },
  100: { name: "ReceiveCouponInterest", title: "کوپن", color: "#81F4C3" },
  200: { name: "ReceiveContractInterest", title: "سود", color: "#43E5A0" },
  300: { name: "SellFacilitiesContract", title: "فروش", color: "#11563A" },
  400: { name: "SellFacilitiesBoard", title: "فروش", color: "#0FA968", suffix: "(تسهیلات)" },
  500: { name: "SellAssetBoard", title: "فروش", color: "#108554", suffix: "(دارایی)" },
  600: { name: "SellAssetContract", title: "فروش", color: "#03301F", suffix: "(دارایی)" },
  700: { name: "SellMaturedBoardAsset", title: "سررسید", color: "#1ACD81", suffix: "(تسهیلات)" },
  800: { name: "PayCouponInterest", title: "سود", color: "#841818" },
  900: { name: "PayContractInterest", title: "سود", color: "#480707" },
  1000: { name: "BuyContractFacility", title: "خرید ", color: "#F83B3B", suffix: "(تسهیلات)" },
  1100: { name: "BuyAssetContract", title: "خرید", color: "#C11414", suffix: "(دارایی)" },
  1200: { name: "BuyAssetBoard", title: "خرید", color: "#FFA0A0" },
  1300: { name: "BuyMaturedFacilities", title: "سررسید", color: "#FF5E5E" }
};

export const TransactionReasonCategory = {
  0: { name: "None", title: "نامشخص" },
  100: { name: "ReceivedInterest", title: "سود (دریافتی)" },
  200: { name: "Sale", title: "فروش" },
  300: { name: "PayedInterest", title: "سود (پرداخت)" },
  400: { name: "Purchase", title: "خرید" }
};

// export const  DepositTransactionsColor = {

//   0:,
//   100: ,
//   200: ,
//   300: ,
//   400:
// }

export const withdrawalTransactionsColor = {};

export const tabs = [
  { id: FUND_TABS.MARKET_RISK, title: "ریسک بازار" },
  { id: FUND_TABS.INTEREST_RISK, title: "ریسک نرخ بهره" },
  { id: FUND_TABS.ADAPTIVE_RISK, title: "ریسک تطبیق" },
  { id: FUND_TABS.OPERATIONAL_RISK, title: "ریسک عملیاتی" },
  { id: FUND_TABS.FUND_ASSETS_LIQUID, title: "ریسک نقدشوندگی" },
  { id: FUND_TABS.LIQUIDITY_RISK, title: "ریسک نقدینگی" },
  { id: FUND_TABS.FOCUS_RISK, title: "ریسک تمرکز" }
];

export const operationRisk_subTabs = [
  { id: FUND_SUB_TABS.EVALUATION, title: "ارزیابی ریسک ها" },
  { id: FUND_SUB_TABS.RISK_MANAGEMENT, title: "استراتژی مدیریت ریسک" }
];

export const focusRisk_subTabs = [
  { id: FOCUS_SUB_TABS.SHAREHOLDERS, title: "سهامدارها" },
  { id: FOCUS_SUB_TABS.ASSETS, title: "دارایی‌ها" }
];

export const getColorByValue = (n?: number) => {
  let color = "text-[#A5F539]";
  if (n === undefined) {
    color = "text-[#676767]";
  } else if (n <= 40) {
    color = "text-[#FF5E5E]";
  } else if (n > 40 && n <= 70) {
    color = "text-[#F1C21B]";
  }

  return color;
};

export const getBorderColorByValue = (n: number) => {
  let color = "border-[#A5F539]";
  if (n <= 40) {
    color = "border-[#FF5E5E]";
  } else if (n > 40 && n <= 70) {
    color = "border-[#F1C21B]";
  }

  return color;
};

export const efficiencyColors = { future: "#26D5C0", past: "#C7DA41", real: "#F4F4F4" };

export const columnChartColorsLight = ["#108554", "#FAEB8E", "#E1AB11", "#E35050"];
export const columnChartColorsDark = ["#0d6a43", "#c8bc72", "#b4890e", "#b64040"];
